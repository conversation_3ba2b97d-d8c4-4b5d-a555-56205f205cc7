<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar>
        <template #left>
          <wd-icon name="chevron-left" size="22px" @click="back" color="#fff"></wd-icon>
        </template>
        <template #title>职位支付</template>
      </CustomNavBar>
      <view
        class="h-400rpx relative px-30rpx bg-cover bg-center bg-no-repeat z--1"
        :style="{ backgroundImage: `url(${releasePost})` }"
      ></view>
    </template>
    <view class="bg-#fff rounded-[20rpx_20rpx_0rpx_0rpx] h-[calc(100vh-490rpx)]">
      <view class="flex items-center justify-center py-30rpx">
        <view
          class="text-#333 text-28rpx m-r-30rpx"
          :class="{
            'text-#A28064 border-b-2 border-#A28064 border-b-solid': activeIndex === index,
            'border-b-2 border-transparent border-b-solid': activeIndex !== index,
          }"
          v-for="(item, index) in payList"
          :key="item.name"
          @click="activeIndex = index"
        >
          {{ item.name }}
        </view>
      </view>
      <scroll-view scroll-x class="whitespace-nowrap w-100">
        <view class="recharge-box">
          <view class="recharge-options flex flex-row justify-between mb-40rpx">
            <view class="recharge-option">
              <view class="option-bonus option-bonus-disabled">月卡</view>
              <view class="text-#9E9E9E text-28rpx m-t-20rpx slash-through relative">￥344</view>
              <view class="option-amount">￥500</view>
            </view>
            <view class="recharge-option recharge-option-active">
              <view class="option-bonus">周卡</view>
              <view class="text-#9E9E9E text-28rpx m-t-20rpx slash-through relative">￥344</view>
              <view class="option-amount option-amount-active">￥1000</view>
            </view>
            <view class="recharge-option">
              <view class="option-bonus option-bonus-disabled">季卡</view>
              <view class="text-#9E9E9E text-28rpx m-t-20rpx slash-through relative">￥344</view>
              <view class="option-amount">￥2000</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import releasePost from '@/static/deepseek/business/release-post.png'
import releasePostSearch from '@/static/deepseek/business/release-post-search.png'
import pay from '@/paymentRelated/img/pany.png'
const { pageStyle } = usePaging({
  style: {
    backgroundImage: `url(${releasePost})`,
  },
})
const activeIndex = ref(0)
const back = () => {
  uni.navigateBack()
}
const payList = ref([
  {
    name: '基础权益',
    icon: pay,
  },
  {
    name: '高级权益',
    icon: pay,
  },
])
const payOptions = ref([
  {
    name: '周卡',
    amount: 500,
    salary: 1000,
    salaryBefore: 1200,
  },
  {
    name: '月卡',
    amount: 1000,
    salary: 1000,
    salaryBefore: 1200,
  },
  {
    name: '季卡',
    amount: 2000,
    salary: 1000,
    salaryBefore: 1200,
  },
])
</script>

<style lang="scss" scoped>
.slash-through::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 80rpx;
  height: 1rpx;
  content: '';
  background: #999;
}
.recharge-box {
  padding: 32rpx 60rpx;
  background: #ffffff;
  border-radius: 40rpx 40rpx 0 0;
  .recharge-options {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .recharge-option {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 180rpx;
    height: 240rpx;
    background: #fff;
    border: 2rpx solid #e5e5e5;
    border-radius: 24rpx;
    .option-bonus {
      position: absolute;
      top: 0;
      left: 50%;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80%;
      height: 38rpx;
      padding: 0 14rpx;
      clip-path: polygon(0% 0%, 100% 0%, 80% 100%, 20% 100%);
      font-size: 22rpx;
      line-height: 38rpx;
      color: #fff;
      background: #bfa16a;
      border-radius: 0 0 16rpx 16rpx;
      transform: translateX(-50%);
    }
    .option-bonus-disabled {
      color: #ffffff;
      background: #dcdada;
    }
    .option-amount {
      margin-top: 24rpx;
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
    }
    .option-amount-active {
      color: #a28064;
    }
  }
  .recharge-option-active {
    border-color: #a28064;
    .option-bonus {
      color: #ffffff;
      background: #a28064;
      border-color: #a28064;
    }
    .option-amount {
      color: #bfa16a;
    }
  }
  .recharge-input-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 32rpx;
    .recharge-input {
      width: 70%;
      height: 80rpx;
      font-size: 28rpx;
      color: #888888;
      text-align: center;
      background: #ebebeb;
      border-radius: 16rpx;
    }
  }
  .recharge-protocol {
    margin-bottom: 32rpx;
    font-size: 22rpx;
    color: #888888;
    text-align: center;
    .protocol-link {
      margin: 0 8rpx;
      color: #3e78ff;
    }
  }
  .recharge-btn {
    width: 70%;
    height: 96rpx;
    margin-top: 16rpx;
    font-size: 32rpx;
    font-weight: normal;
    line-height: 96rpx;
    color: #fff;
    text-align: center;
    background: #797979;
    border: none;
    border-radius: 16rpx;
  }
}
</style>

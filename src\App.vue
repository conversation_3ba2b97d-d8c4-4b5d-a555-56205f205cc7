<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useBaseUrlList } from '@/hooks/common/useBaseUrlList'
const { baseUrlMsgListIntApi } = useBaseUrlList()
const { initEaseMobIM } = useEaseMobIM()
const { newInfoStepPage } = useNewInfoAll()
const { getUserIsLogin } = useUserInfo()
onLaunch(async () => {
  baseUrlMsgListIntApi()
  if (!getUserIsLogin.value) {
    uni.reLaunch({
      url: '/pages/login/index',
    })
    return
  }
  newInfoStepPage()
  initEaseMobIM()
  // #ifdef APP-PLUS
  setTimeout(() => {
    plus.navigator.closeSplashscreen()
  }, 3000)
  // #endif
  console.log('App Launch')
})
onShow(() => {
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* 每个页面公共css */
@import 'style/reset';
@import 'style/common';
@import 'style/iconfont-weapp-icon.css';
</style>

<template>
  <view class="px-50rpx mt-30rpx flex flex-col gap-30rpx">
    <view
      class="h-844rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx overflow-hidden p-20rpx flex flex-col gap-20rpx"
    >
      <wd-textarea
        v-model="releasePostModel.positionDesc"
        placeholder="请输入岗位职责的相关内容"
        :maxlength="1000"
        custom-class="flex-1"
        @input="handleInput"
      />
      <view class="flex justify-end">
        <view
          class="center gap-12rpx bg-#E0ECFF rounded-30rpx h-60rpx w-180rpx"
          @tap="handleDeepseek"
        >
          <wd-img :src="deepseekIcon" width="92rpx" height="16rpx" />
        </view>
      </view>
    </view>
    <view class="flex items-start gap-8rpx">
      <view>
        <wd-icon name="error-circle" size="24rpx" color="#888888" />
      </view>
    </view>
    <gao-ChatSSEClient
      ref="chatSSEClientRef"
      @onOpen="openCore"
      @onError="errorCore"
      @onMessage="messageCore"
      @onFinish="finishCore"
    />
    <wd-popup
      v-model="show"
      position="bottom"
      custom-style="height: 400px;border-radius: 20px 20px 0px 0px;
background: linear-gradient(180deg, #7FA3F7 0%, #A8DEFF 50%, #EBF0FA 100%);padding: 40rpx;"
    >
      <view class="">
        <view class="flex items-center justify-left m-b-20rpx">
          <wd-img :src="deepseekhost" width="22" height="22" />
          <view class="c-#fff text-24rpx">deepseek</view>
        </view>
        <view
          class="h-544rpx w-100 bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx overflow-hidden p-20rpx flex flex-col gap-20rpx"
        >
          <wd-textarea
            v-model="deepseekValue"
            :placeholder="placeholder"
            :maxlength="600"
            custom-class="flex-1"
            show-word-limit
          />
        </view>
        <view class="flex items-center gap-10rpx justify-between m-t-20rpx px-40rpx">
          <view class="flex items-center gap-10rpx" @tap="handleRefresh">
            <wd-img :src="refresh1" width="20" height="20" />
            <view class="c-#333 text-26rpx">重新生成</view>
          </view>
          <view class="flex items-center gap-10rpx" @tap="handleReplace">
            <wd-img :src="refresh1" width="20" height="20" />
            <view class="c-#333 text-26rpx">立即替换</view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { hrCompanyWorkAddressQueryPassList } from '@/service/hrCompanyWorkAddress'
import deepseekIcon from '@/sub_business/static/release/deepseek.png'
import deepseekhost from '@/sub_business/static/release/deepseekhost.png'
import refresh1 from '@/sub_business/static/release/refresh1.png'
import refresh2 from '@/sub_business/static/release/refresh2.png'
const {
  chatSSEClientRef,
  start: sseStart,
  stop: sseStop,
  openCore,
  errorCore,
  finishCore,
  messageCore: sseMessageCore,
} = useDeepseeksse()
// 是否显示深度搜索
const show = ref(false)
const deepseekValue = ref('')
const positionName = ref('')
const placeholder = ref('请输入岗位职责的相关内容')
const { releasePostModel, releaseActiveAddress } = useReleasePost()
async function fetchHrCompanyWorkAddressQueryPassList() {
  const { data } = await hrCompanyWorkAddressQueryPassList({
    entity: {},
    page: 1,
    size: 1,
  })
  const [first] = data.list
  if (first?.id) {
    releaseActiveAddress.value = first
    releasePostModel.value.companyWorkAddressId = first.id
  }
}
// 封装 messageCore
const messageCore = (msg) => {
  sseMessageCore(msg)
  deepseekValue.value += `${msg.data}`
}
// 重新生成
const handleRefresh = () => {
  const msg = deepseekValue.value
  deepseekValue.value = ''
  placeholder.value = '思考中...'
  sseStart('/easyzhipin-ai/aliChat/positionDesc', '', positionName.value, 250)
}
// 立即替换
const handleReplace = () => {
  releasePostModel.value.positionDesc = deepseekValue.value
  show.value = false
}
const handleInput = ({ value }) => {
  deepseekValue.value = value
  console.log(value)
}

function handleSelectWorkerAddress() {
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/sub_business/pages/AddressCenter/index', {
      source: 'release',
    }),
  })
}
// 深度搜索
function handleDeepseek() {
  show.value = true
  if (!deepseekValue.value.length) {
    setTimeout(() => {
      sseStart('/easyzhipin-ai/aliChat/positionDesc', '', positionName.value, 250)
    }, 100)
  }
}
onMounted(() => {
  positionName.value = releasePostModel.value.positionMarkName
    ? releasePostModel.value.positionMarkName
    : releasePostModel.value.positionName
  fetchHrCompanyWorkAddressQueryPassList()
})
</script>

<style lang="scss" scoped>
:deep(.wd-textarea) {
  .wd-textarea__value {
    height: 100%;
  }
  .wd-textarea__inner {
    height: 100%;
  }
}
</style>
